[aws_account]

main_region='us-east-1a'
backup_region='us-east-1b'
output_bucket=nemesis-us-east-1-hotel-data-dev
backup_bucket=nemesis-us-east-1-hotel-data-dev-backup
config_bucket=nemesis-conf-files
config_folder=config

[yelp_api]
api_key=********************************************************************************************************************************
api_key_backup=8MN9seteLCI8y3rd_DxzekkgFuie4PVaNKGY2GE2oi5CMIB9G85aZ1kqepJ35_SkF3oLdjdQz9BTXSoYGZckqLjLyDKI-3359bMf4vIlxlCTSAOu89DBUFyJguzyY3Yx
search_endpoint = https://api.yelp.com/v3/businesses/search
reviews_endpoint = https://api.yelp.com/v3/businesses/{business_id}/reviews
yelp_api_params_path = '../static/params.json'


[mysql]
connection_name=NemesisRDS
host=nemesisrds.cjmjlsxabz75.us-east-1.rds.amazonaws.com
port=3306
username=admin
password=passwordRDS